/**
 * LeetCode controller
 * Handles HTTP requests for LeetCode-related endpoints
 */
import asyncHandler from 'express-async-handler';
import { StatusCodes } from 'http-status-codes';
import { LeetCodeService } from '../services/leetcodeService.js';

/**
 * Get daily LeetCode challenge
 * @route GET /api/v1/leetcode/daily
 * @access Public
 */
const getDailyChallenge = asyncHandler(async (req, res) => {
  const dailyChallenge = await LeetCodeService.fetchDailyChallenge();
  res.status(StatusCodes.OK).json(dailyChallenge);
});

export {
  getDailyChallenge
};
