import { useState } from 'react';
import PropTypes from 'prop-types';
import { Code, Clock, HardDrive, FileText } from 'lucide-react';
import Modal from './Modal';

const SolutionCard = ({ username, code, language, stats, approach }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  // Format language for display
  const getLanguageDisplay = (lang) => {
    const langMap = {
      'python3': 'Python',
      'javascript': 'JavaScript',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C'
    };
    return langMap[lang] || lang;
  };

  return (
    <>
      <div
        className="group relative overflow-hidden rounded-xl p-1 transition-all duration-300 hover:scale-105 cursor-pointer"
        onClick={openModal}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/80 via-purple-500/60 to-purple-400/30 blur-sm group-hover:blur-md transition-all duration-300"></div>
        <div className="relative h-full bg-[#1a0b2e]/90 backdrop-blur-sm rounded-lg p-6 flex flex-col items-center justify-center gap-4">
          <div className="text-4xl text-purple-400 group-hover:text-purple-300 transition-colors duration-300">
            <Code />
          </div>
          <h3 className="text-xl font-semibold text-white text-center group-hover:text-purple-200 transition-colors duration-300">
            {username}'s Solution
          </h3>
          <div className="text-gray-400 text-sm mb-2">
            {getLanguageDisplay(language)}
          </div>

          {stats && (
            <div className="flex items-center justify-center gap-4 text-xs text-gray-400">
              {stats.runtime && (
                <div className="flex items-center gap-1">
                  <Clock size={12} />
                  <span>{stats.runtime}</span>
                </div>
              )}
              {stats.memory && (
                <div className="flex items-center gap-1">
                  <HardDrive size={12} />
                  <span>{stats.memory}</span>
                </div>
              )}
            </div>
          )}

          <div className="text-gray-400 text-xs mt-2">
            Click to view code
          </div>
        </div>
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={`${username}'s Solution (${getLanguageDisplay(language)})`}
      >
        <div className="space-y-4">
          {stats && (
            <div className="bg-gray-800/50 p-3 rounded-md grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center gap-2">
                <Clock size={16} className="text-purple-400" />
                <span className="text-gray-300">Runtime: {stats.runtime || 'N/A'}</span>
              </div>
              <div className="flex items-center gap-2">
                <HardDrive size={16} className="text-purple-400" />
                <span className="text-gray-300">Memory: {stats.memory || 'N/A'}</span>
              </div>
              {stats.runtimePercentile && (
                <div className="col-span-2">
                  <div className="h-1.5 w-full bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
                      style={{ width: `${stats.runtimePercentile}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-400 mt-1">
                    <span>Runtime: Beats {stats.runtimePercentile?.toFixed(2)}%</span>
                    {stats.memoryPercentile && (
                      <span>Memory: Beats {stats.memoryPercentile?.toFixed(2)}%</span>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {approach && (
            <div className="bg-gray-800/50 p-4 rounded-md">
              <div className="flex items-center gap-2 mb-2">
                <FileText size={16} className="text-purple-400" />
                <h3 className="text-white font-medium">Approach</h3>
              </div>
              <pre className="text-gray-300 text-sm whitespace-pre-wrap bg-gray-800/50 p-3 rounded-md">
                {approach}
              </pre>
            </div>
          )}

          <div className="bg-gray-800 p-4 rounded-md overflow-auto max-h-[60vh]">
            <div className="flex items-center gap-2 mb-2">
              <Code size={16} className="text-purple-400" />
              <h3 className="text-white font-medium">Solution Code</h3>
            </div>
            <pre className="text-gray-300 text-sm whitespace-pre-wrap">
              <code>{code}</code>
            </pre>
          </div>
        </div>
      </Modal>
    </>
  );
};

SolutionCard.propTypes = {
  username: PropTypes.string.isRequired,
  code: PropTypes.string.isRequired,
  language: PropTypes.string.isRequired,
  approach: PropTypes.string,
  stats: PropTypes.shape({
    runtime: PropTypes.string,
    memory: PropTypes.string,
    runtimePercentile: PropTypes.number,
    memoryPercentile: PropTypes.number
  })
};

export default SolutionCard;
