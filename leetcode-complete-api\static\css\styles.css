/* Base Styles */
:root {
  --primary-color: #0a84ff;
  --secondary-color: #5856d6;
  --background-color: #f5f5f7;
  --card-color: #ffffff;
  --text-color: #333333;
  --border-color: #e1e1e1;
  --success-color: #34c759;
  --error-color: #ff3b30;
  --info-color: #5ac8fa;
  --warning-color: #ffcc00;
  --code-bg-color: #282c34;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
    'Helvetica Neue', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header */
header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
  border-bottom: 1px solid var(--border-color);
}

header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  color: var(--primary-color);
}

header p {
  font-size: 1.2rem;
  color: #666;
}

/* Tabs */
.tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.tab-btn {
  padding: 10px 20px;
  margin: 0 5px 10px;
  background-color: var(--card-color);
  border: 1px solid var(--border-color);
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  background-color: #f0f0f0;
}

.tab-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Tab Content */
.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

.tab-pane h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: var(--secondary-color);
}

/* Cards */
.card {
  background-color: var(--card-color);
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.card h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.card p {
  margin-bottom: 20px;
  color: #666;
}

/* Form Elements */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  font-size: 1rem;
  font-family: inherit;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
  font-family: 'Courier New', Courier, monospace;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
}

.btn:hover {
  background-color: #0071e3;
}

/* Alert Boxes */
.alert {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 5px;
  border-left: 4px solid;
}

.alert-info {
  background-color: rgba(90, 200, 250, 0.1);
  border-color: var(--info-color);
}

.alert-warning {
  background-color: rgba(255, 204, 0, 0.1);
  border-color: var(--warning-color);
}

.alert-success {
  background-color: rgba(52, 199, 89, 0.1);
  border-color: var(--success-color);
}

.alert-error {
  background-color: rgba(255, 59, 48, 0.1);
  border-color: var(--error-color);
}

.alert strong {
  display: block;
  margin-bottom: 10px;
  color: var(--text-color);
}

.alert ol,
.alert ul {
  margin-left: 20px;
}

.alert li {
  margin-bottom: 5px;
}

.alert a {
  color: var(--primary-color);
  text-decoration: none;
}

.alert a:hover {
  text-decoration: underline;
}

/* Result Container */
.result-container {
  background-color: var(--card-color);
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
  display: none;
}

.result-container h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.result-container pre {
  background-color: var(--code-bg-color);
  border-radius: 5px;
  padding: 15px;
  overflow-x: auto;
  margin-top: 10px;
}

.result-container code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.9rem;
}

/* Polling Status */
.polling-status {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: rgba(90, 200, 250, 0.1);
  border-left: 4px solid var(--info-color);
  font-size: 0.9rem;
  color: var(--text-color);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Footer */
footer {
  text-align: center;
  padding: 20px 0;
  margin-top: 50px;
  border-top: 1px solid var(--border-color);
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  header h1 {
    font-size: 2rem;
  }

  .tab-btn {
    padding: 8px 15px;
    font-size: 0.9rem;
  }

  .card,
  .result-container {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  header h1 {
    font-size: 1.8rem;
  }

  .tab-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    margin: 0 2px 8px;
  }

  .card h3,
  .result-container h3 {
    font-size: 1.3rem;
  }
}
