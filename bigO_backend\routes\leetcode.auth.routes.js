/**
 * LeetCode Authentication routes
 * Defines routes for LeetCode authentication-related endpoints
 */
import express from 'express';
import { checkAuthentication, fetchCookiesFromLeetcode } from '../controllers/auth.controller.js';

const router = express.Router();

/**
 * @route   GET /api/v1/leetcode/auth-check
 * @desc    Check LeetCode authentication status
 * @access  Public
 */
router.get('/auth-check', checkAuthentication);

/**
 * @route   GET /api/v1/leetcode/fetch-cookies
 * @desc    Fetch cookies from LeetCode
 * @access  Public
 */
router.get('/fetch-cookies', fetchCookiesFromLeetcode);

export default router;
