import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { User, LogOut, ChevronDown } from 'lucide-react';
import PropTypes from 'prop-types';

const UserAvatar = ({ user, onLogout }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Get first letter of username or full name for avatar
  const getAvatarLetter = () => {
    if (user?.username) {
      return user.username.charAt(0).toUpperCase();
    }
    if (user?.fullName) {
      return user.fullName.charAt(0).toUpperCase();
    }
    return 'U';
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    setIsDropdownOpen(false);
    onLogout();
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Avatar Button */}
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center gap-2 px-3 py-2 rounded-full hover:bg-purple-800/50 transition-all duration-200 group"
      >
        {/* Avatar Circle */}
        <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-semibold text-lg shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-105">
          {getAvatarLetter()}
        </div>
        
        {/* Username (hidden on mobile) */}
        <span className="hidden md:block text-white font-medium">
          {user?.username || user?.fullName?.split(' ')[0] || 'User'}
        </span>
        
        {/* Dropdown Arrow */}
        <ChevronDown 
          size={16} 
          className={`text-white transition-transform duration-200 ${
            isDropdownOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {/* Dropdown Menu */}
      {isDropdownOpen && (
        <div className="fixed right-4 top-16 w-56 bg-gray-900/95 backdrop-blur-lg rounded-xl border border-purple-500/20 shadow-2xl z-[99999] overflow-hidden">
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-purple-500/20 bg-gradient-to-r from-purple-600/20 to-pink-600/20">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                {getAvatarLetter()}
              </div>
              <div>
                <div className="text-white font-medium text-sm">
                  {user?.fullName || 'User'}
                </div>
                <div className="text-gray-400 text-xs">
                  {user?.email || '<EMAIL>'}
                </div>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-2">
            <Link
              to="/profile"
              className="flex items-center gap-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-purple-500/20 transition-all duration-200"
              onClick={() => setIsDropdownOpen(false)}
            >
              <User size={18} />
              <span>Profile</span>
            </Link>
            

            <div className="border-t border-purple-500/20 my-2"></div>
            
            <button
              onClick={handleLogout}
              className="flex items-center gap-3 px-4 py-3 text-red-400 hover:text-red-300 hover:bg-red-500/20 transition-all duration-200 w-full text-left"
            >
              <LogOut size={18} />
              <span>Logout</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

UserAvatar.propTypes = {
  user: PropTypes.shape({
    username: PropTypes.string,
    fullName: PropTypes.string,
    email: PropTypes.string
  }).isRequired,
  onLogout: PropTypes.func.isRequired
};

export default UserAvatar;
