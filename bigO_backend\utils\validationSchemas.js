/**
 * Validation schemas
 * Defines Joi validation schemas for request validation
 */
import <PERSON><PERSON> from 'joi';

// Headers validation schema - all headers are optional since backend has fallback credentials
const headersSchema = Joi.object({
  cookie: Joi.string().allow('', null).optional(),
  csrfToken: Joi.string().allow('', null).optional(),
  userAgent: Joi.string().allow('', null).optional(),
  origin: Joi.string().allow('', null).optional(),
  referer: Joi.string().allow('', null).optional()
}).unknown(true); // Allow unknown headers

// Submission request validation schema
const submissionRequestSchema = Joi.object({
  lang: Joi.string().default('cpp'),
  question_id: Joi.string().required(),
  typed_code: Joi.string().required(),
  data_input: Joi.string().allow('', null)
});

// Question filters validation schema
const questionFiltersSchema = Joi.object({
  difficulty: Joi.string().valid('EASY', 'MEDIUM', 'HARD').allow(null),
  search: Joi.string().allow('', null)
});

export {
  headersSchema,
  submissionRequestSchema,
  questionFiltersSchema
};
