{"name": "leetcode-api-server", "version": "1.0.0", "description": "Node.js backend for LeetCode API integration", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "lint": "eslint .", "test": "jest"}, "keywords": ["leetcode", "api", "node", "express"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.7", "cors": "^2.8.5", "dotenv": "^16.4.1", "express": "^4.18.2", "express-async-handler": "^1.2.0", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "joi": "^17.12.1", "leetcode-api-server": "file:", "morgan": "^1.10.0", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "nodemon": "^3.0.3", "prettier": "^3.2.5"}}