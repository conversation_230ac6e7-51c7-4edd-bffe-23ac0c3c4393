// DSA Topics data with descriptions and sample problems
export const dsaTopics = [
  {
    id: 'arrays',
    title: 'Arrays',
    description: 'Arrays are a fundamental data structure that store elements of the same type in contiguous memory locations.',
    icon: 'FaListOl',
    problems: [
      {
        id: 1,
        name: 'Two Sum',
        difficulty: 'Easy',
        link: 'https://leetcode.com/problems/two-sum/',
        videoSolution: 'https://www.youtube.com/watch?v=KLlXCFG5TnA',
        viewSolution: 'https://leetcode.com/problems/two-sum/solutions/'
      },
      {
        id: 2,
        name: 'Best Time to Buy and Sell Stock',
        difficulty: 'Easy',
        link: 'https://leetcode.com/problems/best-time-to-buy-and-sell-stock/',
        videoSolution: 'https://www.youtube.com/watch?v=1pkOgXD63yU',
        viewSolution: 'https://leetcode.com/problems/best-time-to-buy-and-sell-stock/solutions/'
      },
      {
        id: 3,
        name: 'Contains Duplicate',
        difficulty: 'Easy',
        link: 'https://leetcode.com/problems/contains-duplicate/',
        videoSolution: 'https://www.youtube.com/watch?v=3OamzN90kPg',
        viewSolution: 'https://leetcode.com/problems/contains-duplicate/solutions/'
      },
      {
        id: 4,
        name: 'Product of Array Except Self',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/product-of-array-except-self/',
        videoSolution: 'https://www.youtube.com/watch?v=bNvIQI2wAjk',
        viewSolution: 'https://leetcode.com/problems/product-of-array-except-self/solutions/'
      },
      {
        id: 5,
        name: 'Maximum Subarray',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/maximum-subarray/',
        videoSolution: 'https://www.youtube.com/watch?v=5WZl3MMT0Eg',
        viewSolution: 'https://leetcode.com/problems/maximum-subarray/solutions/'
      }
    ]
  },
  {
    id: 'strings',
    title: 'Strings',
    description: 'Strings are sequences of characters used to store and manipulate text.',
    icon: 'FaCode',
    problems: [
      {
        id: 1,
        name: 'Valid Anagram',
        difficulty: 'Easy',
        link: 'https://leetcode.com/problems/valid-anagram/',
        videoSolution: 'https://www.youtube.com/watch?v=9UtInBqnCgA',
        viewSolution: 'https://leetcode.com/problems/valid-anagram/solutions/'
      },
      {
        id: 2,
        name: 'Valid Parentheses',
        difficulty: 'Easy',
        link: 'https://leetcode.com/problems/valid-parentheses/',
        videoSolution: 'https://www.youtube.com/watch?v=WTzjTskDFMg',
        viewSolution: 'https://leetcode.com/problems/valid-parentheses/solutions/'
      },
      {
        id: 3,
        name: 'Longest Substring Without Repeating Characters',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/longest-substring-without-repeating-characters/',
        videoSolution: 'https://www.youtube.com/watch?v=wiGpQwVHdE0',
        viewSolution: 'https://leetcode.com/problems/longest-substring-without-repeating-characters/solutions/'
      }
    ]
  },
  {
    id: 'dynamic-programming',
    title: 'Dynamic Programming',
    description: 'Dynamic Programming is a method for solving complex problems by breaking them down into simpler subproblems.',
    icon: 'FaLayerGroup',
    problems: [
      {
        id: 1,
        name: 'Climbing Stairs',
        difficulty: 'Easy',
        link: 'https://leetcode.com/problems/climbing-stairs/',
        videoSolution: 'https://www.youtube.com/watch?v=Y0lT9Fck7qI',
        viewSolution: 'https://leetcode.com/problems/climbing-stairs/solutions/'
      },
      {
        id: 2,
        name: 'Coin Change',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/coin-change/',
        videoSolution: 'https://www.youtube.com/watch?v=H9bfqozjoqs',
        viewSolution: 'https://leetcode.com/problems/coin-change/solutions/'
      },
      {
        id: 3,
        name: 'Longest Increasing Subsequence',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/longest-increasing-subsequence/',
        videoSolution: 'https://www.youtube.com/watch?v=cjWnW0hdF1Y',
        viewSolution: 'https://leetcode.com/problems/longest-increasing-subsequence/solutions/'
      },
      {
        id: 4,
        name: 'Word Break',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/word-break/',
        videoSolution: 'https://www.youtube.com/watch?v=Sx9NNgInc3A',
        viewSolution: 'https://leetcode.com/problems/word-break/solutions/'
      },
      {
        id: 5,
        name: 'Combination Sum IV',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/combination-sum-iv/',
        videoSolution: 'https://www.youtube.com/watch?v=dw2nMCxG0ik',
        viewSolution: 'https://leetcode.com/problems/combination-sum-iv/solutions/'
      }
    ]
  },
  {
    id: 'graphs',
    title: 'Graphs',
    description: 'Graphs are non-linear data structures consisting of nodes and edges used to represent relationships between objects.',
    icon: 'FaNetworkWired',
    problems: [
      {
        id: 1,
        name: 'Number of Islands',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/number-of-islands/',
        videoSolution: 'https://www.youtube.com/watch?v=pV2kpPD66nE',
        viewSolution: 'https://leetcode.com/problems/number-of-islands/solutions/'
      },
      {
        id: 2,
        name: 'Clone Graph',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/clone-graph/',
        videoSolution: 'https://www.youtube.com/watch?v=mQeF6bN8hMk',
        viewSolution: 'https://leetcode.com/problems/clone-graph/solutions/'
      },
      {
        id: 3,
        name: 'Pacific Atlantic Water Flow',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/pacific-atlantic-water-flow/',
        videoSolution: 'https://www.youtube.com/watch?v=s-VkcjHqkGI',
        viewSolution: 'https://leetcode.com/problems/pacific-atlantic-water-flow/solutions/'
      }
    ]
  },
  {
    id: 'trees',
    title: 'Trees',
    description: 'Trees are hierarchical data structures with a root value and subtrees of children with a parent node.',
    icon: 'FaTree',
    problems: [
      {
        id: 1,
        name: 'Maximum Depth of Binary Tree',
        difficulty: 'Easy',
        link: 'https://leetcode.com/problems/maximum-depth-of-binary-tree/',
        videoSolution: 'https://www.youtube.com/watch?v=hTM3phVI6YQ',
        viewSolution: 'https://leetcode.com/problems/maximum-depth-of-binary-tree/solutions/'
      },
      {
        id: 2,
        name: 'Same Tree',
        difficulty: 'Easy',
        link: 'https://leetcode.com/problems/same-tree/',
        videoSolution: 'https://www.youtube.com/watch?v=vRbbcKXCxOw',
        viewSolution: 'https://leetcode.com/problems/same-tree/solutions/'
      },
      {
        id: 3,
        name: 'Binary Tree Level Order Traversal',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/binary-tree-level-order-traversal/',
        videoSolution: 'https://www.youtube.com/watch?v=6ZnyEApgFYg',
        viewSolution: 'https://leetcode.com/problems/binary-tree-level-order-traversal/solutions/'
      }
    ]
  },
  {
    id: 'backtracking',
    title: 'Backtracking',
    description: 'Backtracking is an algorithmic technique for solving problems recursively by trying to build a solution incrementally.',
    icon: 'FaRandom',
    problems: [
      {
        id: 1,
        name: 'Subsets',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/subsets/',
        videoSolution: 'https://www.youtube.com/watch?v=REOH22Xwdkk',
        viewSolution: 'https://leetcode.com/problems/subsets/solutions/'
      },
      {
        id: 2,
        name: 'Combination Sum',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/combination-sum/',
        videoSolution: 'https://www.youtube.com/watch?v=GBKI9VSKdGg',
        viewSolution: 'https://leetcode.com/problems/combination-sum/solutions/'
      },
      {
        id: 3,
        name: 'Permutations',
        difficulty: 'Medium',
        link: 'https://leetcode.com/problems/permutations/',
        videoSolution: 'https://www.youtube.com/watch?v=s7AvT7cGdSo',
        viewSolution: 'https://leetcode.com/problems/permutations/solutions/'
      }
    ]
  }
];
