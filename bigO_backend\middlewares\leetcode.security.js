/**
 * LeetCode Security middleware
 * Provides security-related middleware for LeetCode API endpoints
 */
import cors from 'cors';
import helmet from 'helmet';

/**
 * Configure CORS middleware for LeetCode API
 * @returns {Function} CORS middleware
 */
const configureLeetcodeCors = () => {
  return cors({
    origin: '*', // Allow all origins for now
    credentials: false, // Set to false when using allow_origins=["*"]
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-csrftoken', 'cookie'],
    exposedHeaders: ['Content-Type', 'Authorization']
  });
};

/**
 * Configure Helmet middleware for LeetCode API
 * @returns {Function} Helmet middleware
 */
const configureLeetcodeHelmet = () => {
  return helmet({
    contentSecurityPolicy: false, // Disable CSP for now
    crossOriginEmbedderPolicy: false // Disable COEP for now
  });
};

export {
  configureLeetcodeCors,
  configureLeetcodeHelmet
};
