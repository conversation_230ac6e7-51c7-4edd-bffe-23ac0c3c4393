.daily-heading {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 2rem 0;
  background: linear-gradient(135deg, #7e22ce 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(126, 34, 206, 0.3);
  animation: textGlow 2s ease-in-out infinite;
}

@keyframes textGlow {
  0% {
    text-shadow: 0 0 20px rgba(126, 34, 206, 0.3);
  }
  50% {
    text-shadow: 0 0 30px rgba(126, 34, 206, 0.5);
  }
  100% {
    text-shadow: 0 0 20px rgba(126, 34, 206, 0.3);
  }
}

@keyframes borderGlow {
  0% {
    box-shadow: 0 0 5px rgba(147, 51, 234, 0.15),
      0 0 10px rgba(147, 51, 234, 0.1), 0 0 15px rgba(147, 51, 234, 0.05);
  }
  50% {
    box-shadow: 0 0 8px rgba(147, 51, 234, 0.2),
      0 0 12px rgba(147, 51, 234, 0.15), 0 0 18px rgba(147, 51, 234, 0.1);
  }
  100% {
    box-shadow: 0 0 5px rgba(147, 51, 234, 0.15),
      0 0 10px rgba(147, 51, 234, 0.1), 0 0 15px rgba(147, 51, 234, 0.05);
  }
}

.daily-challenge-card {
  animation: borderGlow 3s ease-in-out infinite;
  transition: transform 0.3s ease;
}

.daily-challenge-card:hover {
  transform: translateY(-3px);
  animation: none;
  box-shadow: 0 0 10px rgba(147, 51, 234, 0.3), 0 0 15px rgba(147, 51, 234, 0.2),
    0 0 20px rgba(147, 51, 234, 0.1);
}

.daily-challenge-card {
  background: rgba(17, 24, 39, 0.8);
  border-radius: 16px;
  padding: 1.5rem;
  margin: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  border: 1px solid transparent;
  background-clip: padding-box;
  transition: all 0.3s ease;
}

.daily-challenge-card::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  margin: -1px;
  border-radius: inherit;
  /* background: linear-gradient(45deg, #7e22ce, #3b82f6); */
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.daily-challenge-card:hover::before {
  opacity: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.date {
  color: #9ca3af;
  font-size: 0.875rem;
}

.difficulty {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #f3f4f6;
}

.question-id {
  color: #9ca3af;
  margin-right: 0.5rem;
}

.title a {
  color: #f3f4f6;
  text-decoration: none;
  transition: color 0.2s ease;
}

.title a:hover {
  color: #7e22ce;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag {
  background: rgba(126, 34, 206, 0.2);
  color: #c084fc;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  border: 1px solid rgba(126, 34, 206, 0.3);
}

.stats-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #9ca3af;
}

.stat-icon {
  font-size: 1rem;
}

.stat-value {
  font-size: 0.875rem;
}

.premium-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: linear-gradient(45deg, #fbbf24, #f59e0b);
  color: #292524;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.premium-icon {
  font-size: 0.875rem;
}

@media (max-width: 640px) {
  .daily-challenge-card {
    padding: 1rem;
  }

  .title {
    font-size: 1rem;
  }

  .tags-container {
    gap: 0.25rem;
  }

  .tag {
    padding: 0.125rem 0.5rem;
  }
}
