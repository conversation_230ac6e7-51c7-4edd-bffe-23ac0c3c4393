/**
 * Solution routes
 * Defines routes for solution-related endpoints
 */
import express from 'express';
import { 
  saveSolution, 
  getSolutionsByProblem, 
  getSolutionsByUser, 
  getSolutionStats 
} from '../controllers/solution.controller.js';
import { isLoggedin } from '../middlewares/auth.middleware.js';

const router = express.Router();

/**
 * @route   POST /api/v1/solutions
 * @desc    Save a solution submission
 * @access  Private (temporarily public for testing)
 */
router.post('/', saveSolution);

/**
 * @route   GET /api/v1/solutions/problem/:problemId
 * @desc    Get all solutions for a specific problem (leaderboard)
 * @access  Public
 */
router.get('/problem/:problemId', getSolutionsByProblem);

/**
 * @route   GET /api/v1/solutions/user/:userId
 * @desc    Get all solutions by a specific user
 * @access  Public
 */
router.get('/user/:userId', getSolutionsByUser);

/**
 * @route   GET /api/v1/solutions/stats
 * @desc    Get solution statistics
 * @access  Public
 */
router.get('/stats', getSolutionStats);

export default router;
