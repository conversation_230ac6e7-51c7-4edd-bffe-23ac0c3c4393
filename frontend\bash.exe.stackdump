Stack trace:
Frame         Function      Args
0007FFFF9F10  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8E10) msys-2.0.dll+0x1FE8E
0007FFFF9F10  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA1E8) msys-2.0.dll+0x67F9
0007FFFF9F10  000210046832 (000210286019, 0007FFFF9DC8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F10  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9F10  000210068E24 (0007FFFF9F20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA1F0  00021006A225 (0007FFFF9F20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB29C00000 ntdll.dll
7FFB281A0000 KERNEL32.DLL
7FFB27390000 KERNELBASE.dll
7FFB27EC0000 USER32.dll
7FFB27990000 win32u.dll
7FFB284E0000 GDI32.dll
7FFB26E30000 gdi32full.dll
7FFB27760000 msvcp_win.dll
7FFB27000000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB29B00000 advapi32.dll
7FFB280A0000 msvcrt.dll
7FFB298D0000 sechost.dll
7FFB297B0000 RPCRT4.dll
7FFB264A0000 CRYPTBASE.DLL
7FFB26D90000 bcryptPrimitives.dll
7FFB28150000 IMM32.DLL
