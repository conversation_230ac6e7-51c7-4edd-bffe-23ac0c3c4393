/**
 * LeetCode Error handling middleware
 * Provides centralized error handling for LeetCode API endpoints
 */
import { StatusCodes } from 'http-status-codes';
import logger from '../utils/logger.js';

/**
 * Not found middleware for LeetCode routes
 * Handles 404 errors for LeetCode routes that don't exist
 */
const leetcodeNotFound = (req, res, next) => {
  const error = new Error(`LeetCode API Not Found - ${req.originalUrl}`);
  res.status(StatusCodes.NOT_FOUND);
  next(error);
};

/**
 * Error handler middleware for LeetCode routes
 * Handles all errors in the LeetCode API endpoints
 */
const leetcodeErrorHandler = (err, req, res, next) => {
  // Log the error but don't expose details to the client
  logger.error(`LeetCode API Error: ${err.message}`);
  logger.debug(err.stack);

  const statusCode = res.statusCode === StatusCodes.OK ? 
    StatusCodes.INTERNAL_SERVER_ERROR : res.statusCode;

  res.status(statusCode).json({
    error: 'LeetCode API Error',
    detail: process.env.NODE_ENV === 'production' ? 
      'An internal server error occurred' : err.message
  });
};

export {
  leetcodeNotFound,
  leetcodeErrorHandler
};
