/**
 * Solution controller
 * Handles HTTP requests for solution-related endpoints
 */
import asyncHandler from 'express-async-handler';
import { StatusCodes } from 'http-status-codes';
import Solution from '../models/solution.model.js';
import User from '../models/user.model.js';

/**
 * Save a solution submission to the database
 * @route POST /api/v1/solutions
 * @access Private (requires authentication)
 */
const saveSolution = asyncHandler(async (req, res) => {
  const {
    problemId,
    problemTitle,
    problemSlug,
    difficulty,
    code,
    language,
    approach,
    submissionId,
    status,
    runtime,
    memory,
    runtimePercentile,
    memoryPercentile,
    totalCorrect,
    totalTestcases
  } = req.body;

  // Get user from request (set by auth middleware) or use default for testing
  let userId, user;

  if (req.user && req.user.id) {
    userId = req.user.id;
    user = await User.findById(userId);
  } else {
    // For testing: find any existing user or use a hardcoded ObjectId
    user = await User.findOne();
    if (!user) {
      // If no users exist, create a minimal solution record without user reference
      const solution = await Solution.create({
        user: null, // Will be set to null for anonymous submissions
        username: 'Anonymous User',
        problemId,
        problemTitle,
        problemSlug,
        difficulty,
        code,
        language,
        approach,
        submissionId,
        status,
        runtime,
        memory,
        runtimePercentile,
        memoryPercentile,
        totalCorrect,
        totalTestcases,
        isAccepted: status === 'Accepted'
      });

      return res.status(StatusCodes.CREATED).json({
        success: true,
        message: 'Solution saved successfully (anonymous)',
        solution: {
          id: solution._id,
          problemTitle: solution.problemTitle,
          status: solution.status,
          runtime: solution.runtime,
          memory: solution.memory,
          createdAt: solution.createdAt
        }
      });
    }
    userId = user._id;
  }

  if (!user) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('User not found');
  }

  // Check if solution already exists for this user and problem
  const existingSolution = await Solution.findOne({
    user: userId,
    problemId: problemId,
    submissionId: submissionId
  });

  if (existingSolution) {
    res.status(StatusCodes.CONFLICT);
    throw new Error('Solution already exists for this submission');
  }

  // Create new solution
  const solution = await Solution.create({
    user: userId,
    username: user.username || user.fullName,
    problemId,
    problemTitle,
    problemSlug,
    difficulty,
    code,
    language,
    approach,
    submissionId,
    status,
    runtime,
    memory,
    runtimePercentile,
    memoryPercentile,
    totalCorrect,
    totalTestcases,
    isAccepted: status === 'Accepted'
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Solution saved successfully',
    solution: {
      id: solution._id,
      problemTitle: solution.problemTitle,
      status: solution.status,
      runtime: solution.runtime,
      memory: solution.memory,
      createdAt: solution.createdAt
    }
  });
});

/**
 * Get all solutions for a specific problem (leaderboard)
 * @route GET /api/v1/solutions/problem/:problemId
 * @access Public
 */
const getSolutionsByProblem = asyncHandler(async (req, res) => {
  const { problemId } = req.params;
  const { limit = 50, page = 1 } = req.query;

  // Get accepted solutions only, sorted by runtime (fastest first)
  const solutions = await Solution.find({
    problemId: problemId,
    isAccepted: true
  })
  .populate('user', 'fullName username')
  .sort({ 
    runtimePercentile: -1, // Higher percentile first (faster)
    createdAt: 1 // Earlier submissions first for tie-breaking
  })
  .limit(parseInt(limit))
  .skip((parseInt(page) - 1) * parseInt(limit));

  // Format solutions for frontend
  const formattedSolutions = solutions.map((solution, index) => ({
    id: solution._id,
    username: solution.username,
    code: solution.code,
    language: solution.language,
    approach: solution.approach,
    timestamp: solution.createdAt,
    problemId: solution.problemId,
    problemTitle: solution.problemTitle,
    stats: {
      runtime: solution.runtime,
      memory: solution.memory,
      runtimePercentile: solution.runtimePercentile,
      memoryPercentile: solution.memoryPercentile
    },
    rank: index + 1
  }));

  res.status(StatusCodes.OK).json({
    success: true,
    count: formattedSolutions.length,
    solutions: formattedSolutions
  });
});

/**
 * Get all solutions by a specific user
 * @route GET /api/v1/solutions/user/:userId
 * @access Public
 */
const getSolutionsByUser = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { limit = 20, page = 1 } = req.query;

  const solutions = await Solution.find({ user: userId })
    .populate('user', 'fullName username')
    .sort({ createdAt: -1 })
    .limit(parseInt(limit))
    .skip((parseInt(page) - 1) * parseInt(limit));

  const formattedSolutions = solutions.map(solution => ({
    id: solution._id,
    problemTitle: solution.problemTitle,
    problemSlug: solution.problemSlug,
    difficulty: solution.difficulty,
    language: solution.language,
    status: solution.status,
    runtime: solution.runtime,
    memory: solution.memory,
    timestamp: solution.createdAt,
    isAccepted: solution.isAccepted
  }));

  res.status(StatusCodes.OK).json({
    success: true,
    count: formattedSolutions.length,
    solutions: formattedSolutions
  });
});

/**
 * Get solution statistics
 * @route GET /api/v1/solutions/stats
 * @access Public
 */
const getSolutionStats = asyncHandler(async (req, res) => {
  const totalSolutions = await Solution.countDocuments();
  const acceptedSolutions = await Solution.countDocuments({ isAccepted: true });
  const uniqueUsers = await Solution.distinct('user').length;
  const uniqueProblems = await Solution.distinct('problemId').length;

  // Get language distribution
  const languageStats = await Solution.aggregate([
    { $match: { isAccepted: true } },
    { $group: { _id: '$language', count: { $sum: 1 } } },
    { $sort: { count: -1 } }
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    stats: {
      totalSolutions,
      acceptedSolutions,
      uniqueUsers,
      uniqueProblems,
      acceptanceRate: totalSolutions > 0 ? ((acceptedSolutions / totalSolutions) * 100).toFixed(2) : 0,
      languageDistribution: languageStats
    }
  });
});

export {
  saveSolution,
  getSolutionsByProblem,
  getSolutionsByUser,
  getSolutionStats
};
