import { config } from "dotenv";
config();
import express from "express";
import cors from "cors";
import cokieParser from "cookie-parser";
import morgan from "morgan";
import bodyParser from "body-parser";
import helmet from "helmet";
import errorMiddleware from "./middlewares/error.middleware.js";
import { leetcodeErrorHandler } from "./middlewares/leetcode.errorHandler.js";
import { configureLeetcodeCors, configureLeetcodeHelmet } from "./middlewares/leetcode.security.js";

// Import routes
import userRoutes from "./routes/user.routes.js";
// Import LeetCode routes
import leetcodeRoutes from "./routes/leetcode.routes.js";
import problemRoutes from "./routes/problem.routes.js";
import submissionRoutes from "./routes/submission.routes.js";
import questionsRoutes from "./routes/questions.routes.js";
import leetcodeAuthRoutes from "./routes/leetcode.auth.routes.js";


const app = express();

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(morgan("dev")); // log every request to the console for debugging purposes

// CORS configuration for main app
app.use(cors(
    {
        origin: [process.env.FRONTEND_URL, 'http://localhost:5173', 'http://localhost:3000'],
        credentials: true
    }
));
app.use(cokieParser());

// Security middleware for LeetCode routes
app.use('/api/v1/leetcode', configureLeetcodeCors());
app.use('/api/v1/leetcode', configureLeetcodeHelmet());


app.use('/ping', (req, res) => {
    res.send('pong');
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'healthy', service: 'BigO Backend with LeetCode API' });
});

//  routes middleware

// Original BigO routes
app.use('/api/v1/user', userRoutes);

// LeetCode API routes
app.use('/api/v1/leetcode', leetcodeRoutes);
app.use('/api/v1/leetcode/problems', problemRoutes);
app.use('/api/v1/leetcode', submissionRoutes);
app.use('/api/v1/leetcode/questions', questionsRoutes);
app.use('/api/v1/leetcode', leetcodeAuthRoutes);

app.all('*', (req, res, next) => {
    res.status(404).send('OOPS!! 404 page Not Found')
});

// Error handling middleware
app.use('/api/v1/leetcode', leetcodeErrorHandler);
app.use(errorMiddleware);

export default app;